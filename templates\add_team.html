{% extends "base.html" %}

{% block title %}إضافة فريق جديد - متابعة الدوريات الأوروبية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4><i class="fas fa-users"></i> إضافة فريق جديد</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.city.label(class="form-label") }}
                        {{ form.city(class="form-control") }}
                        {% if form.city.errors %}
                            <div class="text-danger">
                                {% for error in form.city.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.founded.label(class="form-label") }}
                        {{ form.founded(class="form-control", placeholder="مثال: 1900") }}
                        {% if form.founded.errors %}
                            <div class="text-danger">
                                {% for error in form.founded.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.league_id.label(class="form-label") }}
                        {{ form.league_id(class="form-select") }}
                        {% if form.league_id.errors %}
                            <div class="text-danger">
                                {% for error in form.league_id.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('teams') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> حفظ الفريق
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h6><i class="fas fa-info-circle"></i> نصائح</h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>تأكد من كتابة اسم الفريق بشكل صحيح</li>
                    <li>اختر المدينة التي يقع فيها الفريق</li>
                    <li>سنة التأسيس اختيارية ولكنها مفيدة للمعلومات</li>
                    <li>يجب اختيار الدوري الذي ينتمي إليه الفريق</li>
                    <li>إذا لم تجد الدوري المطلوب، قم بإضافته أولاً</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
