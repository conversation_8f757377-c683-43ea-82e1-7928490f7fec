/* تخصيص الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
    text-align: right;
}

/* تخصيص شريط التنقل */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #ffc107 !important;
}

/* تخصيص البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
}

/* تخصيص الأزرار */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

/* تخصيص النماذج */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تخصيص الجدول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #007bff;
    color: white;
    border: none;
    font-weight: 600;
}

/* تخصيص التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
}

/* تخصيص الشارات */
.badge {
    font-size: 0.9rem;
    padding: 0.5em 0.8em;
}

/* تخصيص الجمبوترون */
.jumbotron {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 15px;
}

/* تخصيص القائمة المنسدلة */
.dropdown-menu {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dropdown-item {
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* تخصيص التذييل */
footer {
    margin-top: auto;
}

/* تخصيص النصوص */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
}

.lead {
    font-weight: 400;
}

/* تخصيص الأيقونات */
.fas, .far {
    margin-left: 8px;
}

/* تخصيص الحاويات */
.container {
    max-width: 1200px;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card, .alert {
    animation: fadeIn 0.5s ease-out;
}

/* تخصيص الألوان */
.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%) !important;
}

/* تخصيص الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
}

/* تخصيص حالة المباريات */
.match-live {
    border-left: 4px solid #dc3545;
}

.match-finished {
    border-left: 4px solid #28a745;
}

.match-scheduled {
    border-left: 4px solid #6c757d;
}

/* تخصيص إضافي للنصوص العربية */
.text-arabic {
    font-family: 'Cairo', sans-serif;
    line-height: 1.8;
}

/* تحسين المظهر العام */
.shadow-sm {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.rounded-lg {
    border-radius: 15px !important;
}
