from flask import Flask, render_template, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_wtf import FlaskForm
from wtforms import StringField, IntegerField, DateField, SelectField, TextAreaField
from wtforms.validators import <PERSON>Required, NumberRange
from config import Config
from datetime import datetime

app = Flask(__name__)
app.config.from_object(Config)
db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class League(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    country = db.Column(db.String(50), nullable=False)
    season = db.Column(db.String(20), nullable=False)
    teams = db.relationship('Team', backref='league', lazy=True)
    matches = db.relationship('Match', backref='league', lazy=True)

class Team(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    city = db.Column(db.String(50), nullable=False)
    founded = db.Column(db.Integer)
    league_id = db.Column(db.Integer, db.ForeignKey('league.id'), nullable=False)
    home_matches = db.relationship('Match', foreign_keys='Match.home_team_id', backref='home_team', lazy=True)
    away_matches = db.relationship('Match', foreign_keys='Match.away_team_id', backref='away_team', lazy=True)

class Match(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    home_team_id = db.Column(db.Integer, db.ForeignKey('team.id'), nullable=False)
    away_team_id = db.Column(db.Integer, db.ForeignKey('team.id'), nullable=False)
    league_id = db.Column(db.Integer, db.ForeignKey('league.id'), nullable=False)
    match_date = db.Column(db.DateTime, nullable=False)
    home_score = db.Column(db.Integer, default=0)
    away_score = db.Column(db.Integer, default=0)
    status = db.Column(db.String(20), default='scheduled')  # scheduled, live, finished

# نماذج الويب
class LeagueForm(FlaskForm):
    name = StringField('اسم الدوري', validators=[DataRequired()])
    country = StringField('البلد', validators=[DataRequired()])
    season = StringField('الموسم', validators=[DataRequired()])

class TeamForm(FlaskForm):
    name = StringField('اسم الفريق', validators=[DataRequired()])
    city = StringField('المدينة', validators=[DataRequired()])
    founded = IntegerField('سنة التأسيس', validators=[NumberRange(min=1800, max=2024)])
    league_id = SelectField('الدوري', coerce=int, validators=[DataRequired()])

class MatchForm(FlaskForm):
    home_team_id = SelectField('الفريق المضيف', coerce=int, validators=[DataRequired()])
    away_team_id = SelectField('الفريق الضيف', coerce=int, validators=[DataRequired()])
    league_id = SelectField('الدوري', coerce=int, validators=[DataRequired()])
    match_date = DateField('تاريخ المباراة', validators=[DataRequired()])
    home_score = IntegerField('أهداف المضيف', default=0)
    away_score = IntegerField('أهداف الضيف', default=0)
    status = SelectField('حالة المباراة', choices=[('scheduled', 'مجدولة'), ('live', 'جارية'), ('finished', 'منتهية')])

# الصفحات الرئيسية
@app.route('/')
def index():
    leagues = League.query.all()
    recent_matches = Match.query.order_by(Match.match_date.desc()).limit(5).all()
    return render_template('index.html', leagues=leagues, recent_matches=recent_matches)

@app.route('/leagues')
def leagues():
    leagues = League.query.all()
    return render_template('leagues.html', leagues=leagues)

@app.route('/league/<int:league_id>')
def league_detail(league_id):
    league = League.query.get_or_404(league_id)
    teams = Team.query.filter_by(league_id=league_id).all()
    matches = Match.query.filter_by(league_id=league_id).order_by(Match.match_date.desc()).all()
    return render_template('league_detail.html', league=league, teams=teams, matches=matches)

@app.route('/teams')
def teams():
    teams = Team.query.all()
    return render_template('teams.html', teams=teams)

@app.route('/matches')
def matches():
    matches = Match.query.order_by(Match.match_date.desc()).all()
    return render_template('matches.html', matches=matches)

# صفحات الإدارة
@app.route('/admin')
def admin():
    return render_template('admin.html')

@app.route('/admin/add_league', methods=['GET', 'POST'])
def add_league():
    form = LeagueForm()
    if form.validate_on_submit():
        league = League(name=form.name.data, country=form.country.data, season=form.season.data)
        db.session.add(league)
        db.session.commit()
        flash('تم إضافة الدوري بنجاح!', 'success')
        return redirect(url_for('leagues'))
    return render_template('add_league.html', form=form)

@app.route('/admin/add_team', methods=['GET', 'POST'])
def add_team():
    form = TeamForm()
    form.league_id.choices = [(l.id, l.name) for l in League.query.all()]
    if form.validate_on_submit():
        team = Team(name=form.name.data, city=form.city.data, 
                   founded=form.founded.data, league_id=form.league_id.data)
        db.session.add(team)
        db.session.commit()
        flash('تم إضافة الفريق بنجاح!', 'success')
        return redirect(url_for('teams'))
    return render_template('add_team.html', form=form)

@app.route('/admin/add_match', methods=['GET', 'POST'])
def add_match():
    form = MatchForm()
    form.league_id.choices = [(l.id, l.name) for l in League.query.all()]
    form.home_team_id.choices = [(t.id, t.name) for t in Team.query.all()]
    form.away_team_id.choices = [(t.id, t.name) for t in Team.query.all()]
    if form.validate_on_submit():
        match = Match(home_team_id=form.home_team_id.data, away_team_id=form.away_team_id.data,
                     league_id=form.league_id.data, match_date=form.match_date.data,
                     home_score=form.home_score.data, away_score=form.away_score.data,
                     status=form.status.data)
        db.session.add(match)
        db.session.commit()
        flash('تم إضافة المباراة بنجاح!', 'success')
        return redirect(url_for('matches'))
    return render_template('add_match.html', form=form)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
