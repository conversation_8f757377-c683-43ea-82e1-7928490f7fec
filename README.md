# تطبيق متابعة الدوريات الأوروبية

تطبيق ويب مبني بـ Flask لمتابعة الدوريات الأوروبية والفرق والمباريات.

## المميزات

- 🏆 إدارة الدوريات الأوروبية
- ⚽ متابعة الفرق والمباريات
- 📊 عرض الإحصائيات والنتائج
- 🎨 واجهة مستخدم عربية جميلة ومتجاوبة
- 📱 متوافق مع الأجهزة المحمولة

## المتطلبات

- Python 3.7 أو أحدث
- pip (مدير حزم Python)

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق

```bash
python app.py
```

### 3. فتح التطبيق

افتح المتصفح وانتقل إلى: `http://localhost:5000`

## استخدام التطبيق

### إضافة دوري جديد
1. انتقل إلى "الإدارة" > "إضافة دوري جديد"
2. أدخل اسم الدوري والبلد والموسم
3. اضغط "حفظ الدوري"

### إضافة فريق جديد
1. انتقل إلى "الإدارة" > "إضافة فريق جديد"
2. أدخل اسم الفريق والمدينة وسنة التأسيس
3. اختر الدوري المناسب
4. اضغط "حفظ الفريق"

### إضافة مباراة جديدة
1. انتقل إلى "الإدارة" > "إضافة مباراة جديدة"
2. اختر الدوري والفريقين
3. حدد تاريخ المباراة والنتيجة (إن وجدت)
4. اختر حالة المباراة
5. اضغط "حفظ المباراة"

## هيكل المشروع

```
car/
├── app.py                 # الملف الرئيسي للتطبيق
├── config.py             # إعدادات التطبيق
├── requirements.txt      # المتطلبات
├── README.md            # هذا الملف
├── templates/           # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── leagues.html
│   ├── teams.html
│   ├── matches.html
│   ├── admin.html
│   ├── add_league.html
│   ├── add_team.html
│   └── add_match.html
└── static/              # الملفات الثابتة
    ├── css/
    │   └── style.css
    ├── js/
    └── images/
```

## قاعدة البيانات

يستخدم التطبيق SQLite كقاعدة بيانات افتراضية. سيتم إنشاء ملف `european_leagues.db` تلقائياً عند تشغيل التطبيق لأول مرة.

### الجداول:
- **League**: الدوريات
- **Team**: الفرق
- **Match**: المباريات

## التخصيص

يمكنك تخصيص التطبيق من خلال:
- تعديل ملف `static/css/style.css` لتغيير التصميم
- تعديل قوالب HTML في مجلد `templates`
- إضافة مميزات جديدة في `app.py`

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى إنشاء Issue في المشروع.

---

**ملاحظة**: هذا التطبيق مصمم للاستخدام المحلي والتعليمي. للاستخدام في الإنتاج، يُنصح بإضافة مميزات الأمان والمصادقة المناسبة.
