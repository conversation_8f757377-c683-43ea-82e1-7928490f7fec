{% extends "base.html" %}

{% block title %}الرئيسية - متابعة الدوريات الأوروبية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
            <h1 class="display-4">مرحباً بك في متابعة الدوريات الأوروبية</h1>
            <p class="lead">تابع أحدث أخبار ونتائج الدوريات الأوروبية الكبرى</p>
            <hr class="my-4">
            <p>يمكنك متابعة الفرق والمباريات وجداول الترتيب لجميع الدوريات الأوروبية</p>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-trophy"></i> الدوريات المتاحة</h5>
            </div>
            <div class="card-body">
                {% if leagues %}
                    <div class="list-group">
                        {% for league in leagues %}
                            <a href="{{ url_for('league_detail', league_id=league.id) }}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ league.name }}</h6>
                                    <small>{{ league.country }}</small>
                                </div>
                                <small>الموسم: {{ league.season }}</small>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد دوريات متاحة حالياً</p>
                    <a href="{{ url_for('add_league') }}" class="btn btn-primary">إضافة دوري جديد</a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-clock"></i> المباريات الأخيرة</h5>
            </div>
            <div class="card-body">
                {% if recent_matches %}
                    {% for match in recent_matches %}
                        <div class="card mb-2">
                            <div class="card-body py-2">
                                <div class="row align-items-center">
                                    <div class="col-4 text-end">
                                        <strong>{{ match.home_team.name }}</strong>
                                    </div>
                                    <div class="col-4 text-center">
                                        {% if match.status == 'finished' %}
                                            <span class="badge bg-success">{{ match.home_score }} - {{ match.away_score }}</span>
                                        {% elif match.status == 'live' %}
                                            <span class="badge bg-danger">جارية</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ match.match_date.strftime('%H:%M') }}</span>
                                        {% endif %}
                                    </div>
                                    <div class="col-4">
                                        <strong>{{ match.away_team.name }}</strong>
                                    </div>
                                </div>
                                <div class="text-center mt-1">
                                    <small class="text-muted">{{ match.match_date.strftime('%Y-%m-%d') }} - {{ match.league.name }}</small>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">لا توجد مباريات متاحة</p>
                    <a href="{{ url_for('add_match') }}" class="btn btn-primary">إضافة مباراة جديدة</a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-chart-bar"></i> إحصائيات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h3>{{ leagues|length }}</h3>
                                <p>دوري</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h3>{{ leagues|sum(attribute='teams')|length if leagues else 0 }}</h3>
                                <p>فريق</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h3>{{ recent_matches|length }}</h3>
                                <p>مباراة أخيرة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <h3>{{ recent_matches|selectattr('status', 'equalto', 'live')|list|length }}</h3>
                                <p>مباراة جارية</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
