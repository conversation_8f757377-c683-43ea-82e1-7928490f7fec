{% extends "base.html" %}

{% block title %}الدوريات - متابعة الدوريات الأوروبية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-trophy"></i> الدوريات الأوروبية</h2>
    <a href="{{ url_for('add_league') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> إضافة دوري جديد
    </a>
</div>

{% if leagues %}
    <div class="row">
        {% for league in leagues %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">{{ league.name }}</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">
                            <strong>البلد:</strong> {{ league.country }}<br>
                            <strong>الموسم:</strong> {{ league.season }}<br>
                            <strong>عدد الفرق:</strong> {{ league.teams|length }}
                        </p>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <a href="{{ url_for('league_detail', league_id=league.id) }}" 
                               class="btn btn-outline-primary">
                                <i class="fas fa-eye"></i> عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-trophy fa-5x text-muted mb-3"></i>
        <h3 class="text-muted">لا توجد دوريات متاحة</h3>
        <p class="text-muted">ابدأ بإضافة دوري جديد لمتابعة الفرق والمباريات</p>
        <a href="{{ url_for('add_league') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus"></i> إضافة دوري جديد
        </a>
    </div>
{% endif %}
{% endblock %}
