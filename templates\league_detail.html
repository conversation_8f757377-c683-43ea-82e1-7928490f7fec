{% extends "base.html" %}

{% block title %}{{ league.name }} - متابعة الدوريات الأوروبية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h1><i class="fas fa-trophy"></i> {{ league.name }}</h1>
                <p class="lead">{{ league.country }} - الموسم {{ league.season }}</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-users"></i> الفرق المشاركة ({{ teams|length }})</h5>
            </div>
            <div class="card-body">
                {% if teams %}
                    <div class="list-group">
                        {% for team in teams %}
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ team.name }}</h6>
                                    <small>{{ team.city }}</small>
                                </div>
                                {% if team.founded %}
                                    <small class="text-muted">تأسس عام {{ team.founded }}</small>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد فرق مسجلة في هذا الدوري</p>
                    <a href="{{ url_for('add_team') }}" class="btn btn-primary">إضافة فريق</a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-calendar"></i> المباريات الأخيرة</h5>
            </div>
            <div class="card-body">
                {% if matches %}
                    {% for match in matches[:10] %}
                        <div class="card mb-2">
                            <div class="card-body py-2">
                                <div class="row align-items-center">
                                    <div class="col-4 text-end">
                                        <strong>{{ match.home_team.name }}</strong>
                                    </div>
                                    <div class="col-4 text-center">
                                        {% if match.status == 'finished' %}
                                            <span class="badge bg-success fs-6">{{ match.home_score }} - {{ match.away_score }}</span>
                                        {% elif match.status == 'live' %}
                                            <span class="badge bg-danger">جارية</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ match.match_date.strftime('%H:%M') }}</span>
                                        {% endif %}
                                    </div>
                                    <div class="col-4">
                                        <strong>{{ match.away_team.name }}</strong>
                                    </div>
                                </div>
                                <div class="text-center mt-1">
                                    <small class="text-muted">{{ match.match_date.strftime('%Y-%m-%d %H:%M') }}</small>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                    {% if matches|length > 10 %}
                        <div class="text-center">
                            <a href="{{ url_for('matches') }}" class="btn btn-outline-primary">عرض جميع المباريات</a>
                        </div>
                    {% endif %}
                {% else %}
                    <p class="text-muted">لا توجد مباريات مسجلة في هذا الدوري</p>
                    <a href="{{ url_for('add_match') }}" class="btn btn-primary">إضافة مباراة</a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-chart-bar"></i> إحصائيات الدوري</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h3>{{ teams|length }}</h3>
                                <p>فريق</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h3>{{ matches|length }}</h3>
                                <p>مباراة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h3>{{ matches|selectattr('status', 'equalto', 'finished')|list|length }}</h3>
                                <p>مباراة منتهية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <h3>{{ matches|selectattr('status', 'equalto', 'scheduled')|list|length }}</h3>
                                <p>مباراة مجدولة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
