{% extends "base.html" %}

{% block title %}إضافة مباراة جديدة - متابعة الدوريات الأوروبية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h4><i class="fas fa-calendar"></i> إضافة مباراة جديدة</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.league_id.label(class="form-label") }}
                                {{ form.league_id(class="form-select") }}
                                {% if form.league_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.league_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.match_date.label(class="form-label") }}
                                {{ form.match_date(class="form-control") }}
                                {% if form.match_date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.match_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-5">
                            <div class="mb-3">
                                {{ form.home_team_id.label(class="form-label") }}
                                {{ form.home_team_id(class="form-select") }}
                                {% if form.home_team_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.home_team_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <label class="form-label">&nbsp;</label>
                            <div class="fs-3 fw-bold text-muted">VS</div>
                        </div>
                        <div class="col-md-5">
                            <div class="mb-3">
                                {{ form.away_team_id.label(class="form-label") }}
                                {{ form.away_team_id(class="form-select") }}
                                {% if form.away_team_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.away_team_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                {{ form.home_score.label(class="form-label") }}
                                {{ form.home_score(class="form-control") }}
                                {% if form.home_score.errors %}
                                    <div class="text-danger">
                                        {% for error in form.home_score.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                {{ form.away_score.label(class="form-label") }}
                                {{ form.away_score(class="form-control") }}
                                {% if form.away_score.errors %}
                                    <div class="text-danger">
                                        {% for error in form.away_score.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-select") }}
                                {% if form.status.errors %}
                                    <div class="text-danger">
                                        {% for error in form.status.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('matches') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save"></i> حفظ المباراة
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h6><i class="fas fa-info-circle"></i> نصائح</h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>اختر الدوري أولاً لتحديد المباراة</li>
                    <li>تأكد من اختيار فريقين مختلفين</li>
                    <li>حدد تاريخ المباراة بدقة</li>
                    <li>النتيجة اختيارية ويمكن تركها 0-0 للمباريات المجدولة</li>
                    <li>اختر حالة المباراة المناسبة (مجدولة، جارية، منتهية)</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
