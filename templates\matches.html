{% extends "base.html" %}

{% block title %}المباريات - متابعة الدوريات الأوروبية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-calendar"></i> المباريات</h2>
    <a href="{{ url_for('add_match') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> إضافة مباراة جديدة
    </a>
</div>

{% if matches %}
    <div class="row">
        {% for match in matches %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header 
                        {% if match.status == 'finished' %}bg-success
                        {% elif match.status == 'live' %}bg-danger
                        {% else %}bg-secondary{% endif %} text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>{{ match.league.name }}</span>
                            <span class="badge bg-light text-dark">
                                {% if match.status == 'finished' %}انتهت
                                {% elif match.status == 'live' %}جارية
                                {% else %}مجدولة{% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <div class="row align-items-center mb-3">
                                <div class="col-5 text-end">
                                    <h6 class="mb-0">{{ match.home_team.name }}</h6>
                                    <small class="text-muted">{{ match.home_team.city }}</small>
                                </div>
                                <div class="col-2 text-center">
                                    {% if match.status == 'finished' %}
                                        <h4 class="mb-0">{{ match.home_score }} - {{ match.away_score }}</h4>
                                    {% elif match.status == 'live' %}
                                        <h4 class="mb-0 text-danger">{{ match.home_score }} - {{ match.away_score }}</h4>
                                        <small class="text-danger">جارية</small>
                                    {% else %}
                                        <h6 class="mb-0">VS</h6>
                                    {% endif %}
                                </div>
                                <div class="col-5">
                                    <h6 class="mb-0">{{ match.away_team.name }}</h6>
                                    <small class="text-muted">{{ match.away_team.city }}</small>
                                </div>
                            </div>
                            <hr>
                            <p class="card-text">
                                <strong>التاريخ:</strong> {{ match.match_date.strftime('%Y-%m-%d') }}<br>
                                <strong>الوقت:</strong> {{ match.match_date.strftime('%H:%M') }}
                            </p>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <a href="{{ url_for('league_detail', league_id=match.league.id) }}" 
                               class="btn btn-outline-primary">
                                <i class="fas fa-trophy"></i> الدوري
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-calendar fa-5x text-muted mb-3"></i>
        <h3 class="text-muted">لا توجد مباريات متاحة</h3>
        <p class="text-muted">ابدأ بإضافة مباراة جديدة</p>
        <a href="{{ url_for('add_match') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus"></i> إضافة مباراة جديدة
        </a>
    </div>
{% endif %}
{% endblock %}
